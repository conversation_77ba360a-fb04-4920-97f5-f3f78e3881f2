<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>记忆碎片扫描</title>
<style>
    body {
        background-color: black;
        color: #00ff00;
        font-family: Consolas, "Courier New", monospace;
        padding: 10px;
        font-size: 14px;
        line-height: 1.4em;
        white-space: pre;
        overflow-y: scroll;
    }
    .warning {
        color: #ffff00; /* 黄色 */
    }
    .error {
        color: #ff5555; /* 红色 */
    }
    .memory {
        color: #00bfff; /* 蓝色，用于林夏记忆 */
    }
</style>
</head>
<body>
<div id="console"></div>

<script>
// 模拟log数据
let logs = [];
for (let i = 1; i <= 1278; i++) {
    let base = `[INFO] 加载记忆碎片：/home/<USER>/mem_${String(i).padStart(4,'0')} ... done\n` +
               `[WARNING] 未登记记忆碎片：/home/<USER>/mem_${String(i).padStart(4,'0')}`;
    
    // 每隔一定概率插入损坏碎片
    if (Math.random() < 0.05) {
        base += `\n[WARNING] 发现损坏的记忆碎片：/home/<USER>/mem_${String(i).padStart(4,'0')}`;
    }
    
    // 每隔一定概率插入林夏的记忆片段
    if (Math.random() < 0.1) {
        const memoryFragments = [
            '林夏："……那天的雨下得很大，我等你很久……"',
            '林夏："我好像看到了你笑的样子，像以前一样……"',
            '林夏："你记得吗？第一次看星星的那天……"',
            '林夏："为什么你会离开呢？"',
            '林夏："不要忘记我……"'
        ];
        let randomMemory = memoryFragments[Math.floor(Math.random() * memoryFragments.length)];
        base += `\n[MEMORY] <span class="memory">${randomMemory}</span>`;
    }

    logs.push(base);
}

// 最后的总结
logs.push(
    `[SUMMARY] 记忆碎片扫描完成：\n` +
    `    总计：1278 条\n` +
    `    损坏：46 条\n` +
    `    未登记：1278 条\n\n` +
    `RESULT = MEMORY_CHECK_DONE`
);

let consoleDiv = document.getElementById("console");
let index = 0;

// 逐行打印效果
function printLog() {
    if (index < logs.length) {
        let line = logs[index]
            .replace(/\[WARNING\]/g, '<span class="warning">[WARNING]</span>')
            .replace(/\[ERROR\]/g, '<span class="error">[ERROR]</span>');
        consoleDiv.innerHTML += line + "\n";
        window.scrollTo(0, document.body.scrollHeight); // 自动滚动到底部
        index++;
        setTimeout(printLog, 30); // 打印速度，越小越快
    }
}

printLog();
</script>
</body>
</html>
