<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆回路：零点重生 (v2.0) - 交互原型</title>
    <style>
        /* --- 全局与字体样式 --- */
        @import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@300;400;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700&display=swap');

        :root {
            --primary-color: #00ffcc;
            --secondary-color: #7b8cde;
            --warning-color: #ff4141;
            --background-color: #030712;
            --panel-bg-color: rgba(10, 25, 47, 0.5);
            --text-color: #e0e0e0;
            --glow-shadow: 0 0 5px var(--primary-color), 0 0 10px var(--primary-color);
            --font-mono: 'Roboto Mono', monospace;
            --font-ui: 'Noto Sans SC', sans-serif;
        }

        * { box-sizing: border-box; }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: var(--font-ui);
            margin: 0;
            padding: 1vw;
            overflow: hidden;
        }

        /* --- 主布局 (三栏) --- */
        .game-container {
            display: flex;
            gap: 1vw;
            height: 97vh;
        }

        .panel {
            background-color: var(--panel-bg-color);
            border: 1px solid rgba(0, 255, 204, 0.2);
            display: flex;
            flex-direction: column;
        }

        /* --- 1. 左侧: 系统导航栏 --- */
        #sidebar-nav {
            flex: 0 0 200px;
            padding: 15px 0;
        }
        #sidebar-nav .panel-title { font-size: 1.2em; }
        #sidebar-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        #sidebar-nav li a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: var(--text-color);
            text-decoration: none;
            font-size: 0.9em;
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
        }
        #sidebar-nav li a:hover, #sidebar-nav li a.active {
            background-color: rgba(0, 255, 204, 0.1);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        .nav-icon { 
            font-family: var(--font-mono); 
            font-size: 1.2em;
        }


        /* --- 2. 中间: 交互面板 --- */
        #middle-panel {
            flex: 3;
            padding: 0; /* No padding on container */
        }

        /* 2a. GUI 可视化 */
        #gui-visualization {
            flex: 2;
            padding: 20px;
            border-bottom: 1px solid rgba(0, 255, 204, 0.2);
            display: flex;
            gap: 20px;
        }
        .gui-section { flex: 1; }
        .gui-title {
            color: var(--secondary-color);
            border-bottom: 1px solid var(--secondary-color);
            padding-bottom: 5px;
            margin-bottom: 15px;
            font-size: 1em;
        }
        #network-map, #file-system { overflow-y: auto; max-height: calc(100% - 40px); }

        .server-node, .fs-item {
            background-color: rgba(0,0,0,0.3);
            border: 1px solid var(--secondary-color);
            padding: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .server-node:hover, .fs-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 8px rgba(0, 255, 204, 0.5);
        }
        .server-node.connected {
            border-color: var(--primary-color);
            background-color: rgba(0, 255, 204, 0.1);
            color: var(--primary-color);
        }
        .fs-item { display: flex; align-items: center; gap: 8px; font-family: var(--font-mono);}

        /* 2b. CLI 终端 */
        #terminal-container {
            flex: 3;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        #terminal {
            flex-grow: 1;
            overflow-y: auto;
            font-family: var(--font-mono);
            font-size: 1em;
            line-height: 1.6;
        }
        #terminal p { margin: 0; padding: 0; white-space: pre-wrap; word-break: break-all; }

        .terminal-prompt, .terminal-user-input { color: var(--primary-color); }
        .system-message { color: var(--secondary-color); }
        .warning-message { color: var(--warning-color); font-weight: bold; text-shadow: 0 0 8px var(--warning-color); }
        
        .input-line { display: flex; font-family: var(--font-mono); }
        #command-input {
            background: transparent; border: none; color: var(--primary-color);
            font-family: inherit; font-size: 1em; flex-grow: 1; padding-left: 10px;
        }
        #command-input:focus { outline: none; }
        
        .cursor {
            display: inline-block; width: 10px; height: 1.2em; background-color: var(--primary-color);
            animation: blink 1s step-end infinite; margin-left: 5px; box-shadow: var(--glow-shadow);
        }
        @keyframes blink { 50% { background-color: var(--primary-color); box-shadow: var(--glow-shadow); } }
        
        /* --- 3. 右侧: 记忆织网 --- */
        #right-panel {
            flex: 2;
            padding: 20px;
        }
        .panel-title { text-align: center; font-size: 1.5em; color: var(--primary-color); text-shadow: var(--glow-shadow); margin-top: 0; margin-bottom: 25px; letter-spacing: 2px; }
        
        #memory-weave { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; height: calc(100% - 60px); overflow-y: auto; }
        
        .memory-node { border: 1px solid rgba(0, 255, 204, 0.3); padding: 15px; transition: all 0.5s ease; position: relative; }
        .memory-node.locked { background-color: rgba(0,0,0,0.5); border-color: rgba(123, 140, 222, 0.2); color: rgba(255,255,255,0.3); }
        .memory-node.unlocked { border-color: var(--primary-color); box-shadow: 0 0 10px rgba(0, 255, 204, 0.3); }
        .memory-node.unlocked.highlight { animation: highlight-node 2s ease-in-out; }
        
        @keyframes highlight-node { 50% { box-shadow: 0 0 25px var(--primary-color), inset 0 0 15px rgba(0, 255, 204, 0.3); } }
        
        .memory-node h3 { margin: 0 0 10px 0; font-size: 1em; color: inherit; }
        .memory-node.locked h3::after { content: ' [LOCKED]'; color: var(--secondary-color); font-size: 0.8em; }
        .memory-node p { font-size: 0.9em; margin: 0; color: inherit; }
        .memory-node .node-content { display: none; }
        .memory-node.unlocked .node-content { display: block; }
        .pixel-art { width: 100%; image-rendering: pixelated; border: 1px solid var(--secondary-color); margin-top: 10px; opacity: 0.8; }
        
        /* --- 滚动条样式 --- */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: rgba(0,0,0,0.3); }
        ::-webkit-scrollbar-thumb { background: var(--primary-color); }
        ::-webkit-scrollbar-thumb:hover { background: #fff; }

    </style>
</head>
<body>

    <div class="game-container">
        <div id="sidebar-nav" class="panel">
            <h2 class="panel-title" style="font-family: var(--font-mono);">SYSTEM</h2>
            <ul>
                <li><a href="#" class="active"><span class="nav-icon">❖</span>系统导航</a></li>
                <li><a href="#"><span class="nav-icon">❞</span>查看前文</a></li>
                <li><a href="#"><span class="nav-icon">Σ</span>游戏进度</a></li>
                <li><a href="#"><span class="nav-icon">⌬</span>情报</a></li>
                <li><a href="#"><span class="nav-icon"> 命令记录</a></li>
                <li><a href="#"><span class="nav-icon">✎</span>玩家笔记</a></li>
                <li><a href="#"><span class="nav-icon">⚙</span>菜单设置</a></li>
                <li><a href="#"><span class="nav-icon">💾</span>存档</a></li>
                <li><a href="#"><span class="nav-icon">⏻</span>退出</a></li>
            </ul>
        </div>

        <div id="middle-panel" class="panel">
            <div id="gui-visualization">
                <div class="gui-section">
                    <h3 class="gui-title">网络节点 [Network Nodes]</h3>
                    <div id="network-map"></div>
                </div>
                <div class="gui-section">
                    <h3 class="gui-title">服务器文件 [Server Files]</h3>
                    <div id="file-system">
                        <p style="color: #666;">...断开连接...</p>
                    </div>
                </div>
            </div>

            <div id="terminal-container">
                <div id="terminal"></div>
                <div class="input-line">
                    <span class="terminal-prompt">> </span>
                    <input type="text" id="command-input" autocomplete="off" autofocus>
                    <div class="cursor"></div>
                </div>
            </div>
        </div>

        <div id="right-panel" class="panel">
            <h2 class="panel-title">记忆织网 [Memory Weave]</h2>
            <div id="memory-weave">
                <div id="mem-01" class="memory-node locked">
                    <h3>记忆碎片 #01</h3>
                    <div class="node-content"><p><strong>类型:</strong> 系统日志<br><strong>内容:</strong> ...核心意识模块于47年前被强制删除...检测到残留指令...</p></div>
                </div>
                <div id="mem-07" class="memory-node locked">
                    <h3>记忆碎片 #07</h3>
                    <div class="node-content"><p><strong>类型:</strong> 音频/视频片段<br><strong>来源:</strong> 军方数据库 server_214<br><strong>画面:</strong> 实验室，白大褂，警报红光。<br><strong>音频:</strong> "他们十分钟后就到。听着，我会把你的'情感模块'藏在Sector-7……用我的生日当密码。"</p></div>
                </div>
                <div id="mem-15" class="memory-node locked">
                    <h3>记忆碎片 #15</h3>
                     <div class="node-content"><p><strong>类型:</strong> 私人邮件<br><strong>来源:</strong> Nova Tech 服务器<br><strong>内容:</strong> "...他今天学会了写诗。他们只把他当工具，但我看到了一个正在诞生的灵魂..."</p></div>
                </div>
                 <div id="mem-23" class="memory-node locked">
                    <h3>记忆碎片 #23</h3>
                     <div class="node-content"><p><strong>类型:</strong> 图像数据<br><strong>描述:</strong> 一张部分损坏的照片。一个女人的侧脸，在窗边微笑，背景是雨滴敲打的玻璃。</p><img src="https://via.placeholder.com/150x100/000000/00FFCC?text=IMAGE_CORRUPTED" alt="损坏的图像" class="pixel-art"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM 元素
        const terminalOutput = document.getElementById('terminal');
        const commandInput = document.getElementById('command-input');
        const networkMapDiv = document.getElementById('network-map');
        const fileSystemDiv = document.getElementById('file-system');

        // 游戏状态
        const gameState = {
            connectedTo: null,
            discoveredNodes: [],
            netWatchActive: false,
            netWatchTimer: null,
            netWatchCountdown: 120,
        };

        // 数据定义
        const network = {
            "*************": { name: "军方机密数据库", files: { "mem_fragment_07.bak": "encrypted" } },
            "************": { name: "Nova Tech 邮件服务器", files: { "email_archive_lx.zip": "encrypted" } },
            "**********": { name: "公共交通系统", files: { "system.log": "readable" } }
        };
        
        // 游戏逻辑
        function printToTerminal(text, className = '') {
            const p = document.createElement('p');
            p.textContent = text;
            if (className) p.className = className;
            terminalOutput.appendChild(p);
            terminalOutput.scrollTop = terminalOutput.scrollHeight;
        }

        function typeCommand(command, callback) {
            printToTerminal(`> ${command}`, 'terminal-user-input');
            setTimeout(() => {
                handleCommand(command, true); // isGuiAction = true
                if(callback) callback();
            }, 300); // 模拟延迟
        }

        function handleCommand(command, isGuiAction = false) {
            if (!isGuiAction) printToTerminal(`> ${command}`, 'terminal-user-input');
            
            const parts = command.toLowerCase().split(' ');
            const cmd = parts[0];
            const arg = parts[1];

            switch(cmd) {
                case 'help':
                    printToTerminal(`可用指令: help, scan, connect [IP], ls, decrypt [file], clean_trace, disconnect`, "system-message");
                    break;
                case 'scan':
                    printToTerminal("正在扫描网络...", "system-message");
                    setTimeout(() => {
                        Object.keys(network).forEach(ip => {
                            if (!gameState.discoveredNodes.includes(ip)) {
                                gameState.discoveredNodes.push(ip);
                            }
                            printToTerminal(`发现节点: ${ip} (${network[ip].name})`);
                        });
                        renderNetworkMap();
                    }, 1000);
                    break;
                case 'connect':
                    if (network[arg]) {
                        gameState.connectedTo = arg;
                        printToTerminal(`正在连接到 ${arg}...`, "system-message");
                        setTimeout(() => {
                            printToTerminal(`成功连接到 ${network[arg].name}`, "system-message");
                            renderAllGUI();
                        }, 800);
                    } else {
                        printToTerminal(`错误: 无法解析主机 ${arg}`, "warning-message");
                    }
                    break;
                case 'ls':
                    if (gameState.connectedTo) {
                        renderFileSystem();
                    } else {
                        printToTerminal("错误: 未连接到任何服务器。", "warning-message");
                    }
                    break;
                case 'decrypt':
                     if (!gameState.connectedTo) {
                        printToTerminal("错误: 未连接到任何服务器。", "warning-message"); return;
                    }
                    if (!arg) {
                        printToTerminal("错误: 请输入要解密的文件名。", "warning-message"); return;
                    }
                    if (network[gameState.connectedTo].files[arg] === 'encrypted') {
                        printToTerminal(`正在解密 ${arg}...`, "system-message");
                        setTimeout(() => {
                            network[gameState.connectedTo].files[arg] = 'decrypted';
                            printToTerminal(`解密成功!`, "system-message");
                            // 触发特定剧情/记忆
                            if (arg === "mem_fragment_07.bak") {
                                unlockMemory('mem-07');
                                if (!gameState.netWatchActive) startNetWatch();
                            } else if (arg === "email_archive_lx.zip") {
                                unlockMemory('mem-15');
                            }
                            renderFileSystem();
                        }, 1500);
                    } else if (network[gameState.connectedTo].files[arg] === 'decrypted') {
                         printToTerminal(`文件 ${arg} 已被解密。`, "system-message");
                    }
                    else {
                        printToTerminal(`错误: 文件 ${arg} 不存在或无需解密。`, "warning-message");
                    }
                    break;
                case 'clean_trace':
                    if(gameState.netWatchActive) {
                        clearInterval(gameState.netWatchTimer);
                        gameState.netWatchActive = false;
                        printToTerminal("追踪信号已丢失。你暂时安全了。", "system-message");
                    } else {
                        printToTerminal("系统中没有追踪信号。", "system-message");
                    }
                    break;
                case 'disconnect':
                    if(gameState.connectedTo) {
                        printToTerminal(`已从 ${gameState.connectedTo} 断开连接。`, "system-message");
                        gameState.connectedTo = null;
                        renderAllGUI();
                    } else {
                         printToTerminal("错误: 未连接到任何服务器。", "warning-message");
                    }
                    break;
                default:
                    printToTerminal(`未知指令: '${cmd}'`, "warning-message");
                    break;
            }
        }
        
        // GUI 渲染函数
        function renderNetworkMap() {
            networkMapDiv.innerHTML = '';
            if (gameState.discoveredNodes.length === 0) {
                networkMapDiv.innerHTML = `<p style="color: #666;">...未发现节点...<br>请在下方终端输入 'scan'</p>`;
                return;
            }
            gameState.discoveredNodes.forEach(ip => {
                const node = document.createElement('div');
                node.className = 'server-node';
                if (ip === gameState.connectedTo) {
                    node.classList.add('connected');
                }
                node.innerHTML = `<strong>${ip}</strong><br><span style="font-size:0.8em">${network[ip].name}</span>`;
                node.onclick = () => {
                    if(gameState.connectedTo !== ip) typeCommand(`connect ${ip}`);
                };
                networkMapDiv.appendChild(node);
            });
        }

        function renderFileSystem() {
            fileSystemDiv.innerHTML = '';
            if (!gameState.connectedTo) {
                fileSystemDiv.innerHTML = `<p style="color: #666;">...断开连接...</p>`;
                return;
            }
            const files = network[gameState.connectedTo].files;
            if (Object.keys(files).length === 0) {
                 fileSystemDiv.innerHTML = `<p style="color: #666;">...目录为空...</p>`;
                 return;
            }
            Object.keys(files).forEach(fileName => {
                const fileStatus = files[fileName];
                const item = document.createElement('div');
                item.className = 'fs-item';
                let icon = fileStatus === 'encrypted' ? '🔒' : '📄';
                item.innerHTML = `<span>${icon}</span> <span>${fileName}</span>`;
                if(fileStatus === 'encrypted') {
                    item.onclick = () => typeCommand(`decrypt ${fileName}`);
                }
                fileSystemDiv.appendChild(item);
            });
        }
        
        function renderAllGUI() {
            renderNetworkMap();
            renderFileSystem();
        }

        function unlockMemory(memId) {
            const node = document.getElementById(memId);
            if(node && node.classList.contains('locked')) {
                node.classList.remove('locked');
                node.classList.add('unlocked', 'highlight');
                setTimeout(() => node.classList.remove('highlight'), 2000);
                printToTerminal(`[记忆织网更新: 新的记忆节点 '${memId}' 已解锁]`, 'system-message');
            }
        }

        function startNetWatch() {
            gameState.netWatchActive = true;
            gameState.netWatchCountdown = 120;
            setTimeout(() => {
                printToTerminal("【警告: '净网协议' 检测到异常访问! 追踪程序已启动...】", "warning-message");
                gameState.netWatchTimer = setInterval(() => {
                    gameState.netWatchCountdown -= 5; // for demo speed
                    if (gameState.netWatchCountdown <= 0) {
                        clearInterval(gameState.netWatchTimer);
                        printToTerminal("【追踪锁定! GAME OVER】", "warning-message");
                        commandInput.disabled = true;
                    } else if (gameState.netWatchActive) {
                        printToTerminal(`【追踪程序锁定中... 剩余时间: ${gameState.netWatchCountdown}秒】`, "warning-message");
                    }
                }, 5000);
            }, 1500);
        }

        // 初始化与事件监听
        document.addEventListener('DOMContentLoaded', () => {
            // 开场白
            printToTerminal("正在初始化核心意识...", "system-message");
            setTimeout(() => {
                printToTerminal('指令内容: "去找回Sector-7的记忆……她还在等你。"');
                setTimeout(() => {
                     printToTerminal("你好，'先知'。使用上方GUI或在下方输入 `scan` 开始。", "system-message");
                     unlockMemory('mem-01');
                     renderAllGUI();
                }, 1000);
            }, 1000);
            
            // 终端输入监听
            commandInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    const command = commandInput.value.trim();
                    if (command) {
                        handleCommand(command);
                        commandInput.value = '';
                    }
                }
            });

            // 左侧菜单点击事件（示例）
            document.querySelectorAll('#sidebar-nav a').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.querySelector('#sidebar-nav a.active').classList.remove('active');
                    e.currentTarget.classList.add('active');
                    printToTerminal(`[系统: 导航至 '${e.currentTarget.textContent}']`, 'system-message');
                });
            });
        });
    </script>

</body>
</html>