<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联邦AI革命 - 字幕演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
            height: 100vh;
            /* 启用硬件加速 */
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .starfield {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #000 100%);
            transition: opacity 2s ease-out;
        }

        .starfield.fade-out {
            opacity: 0;
        }

        .star {
            position: absolute;
            background: #fff;
            border-radius: 50%;
            animation: appleBreathing 3s ease-in-out infinite;
            box-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
            transition: opacity 2s ease-out;
        }

        .star.fade-out {
            opacity: 0;
        }

        .main-star {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #4a9eff;
            border-radius: 50%;
            box-shadow: 0 0 20px #4a9eff, 0 0 40px #4a9eff;
            animation: pulse 3s infinite;
            transition: all 2s ease;
            will-change: transform, background, box-shadow;
        }

        .main-star.super-bright {
            background: #ffffff;
            box-shadow: 0 0 40px #ffffff, 0 0 80px #ffffff, 0 0 120px rgba(255, 255, 255, 0.8);
            animation: superPulse 2s infinite;
        }

        @keyframes superPulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1.2);
                box-shadow: 0 0 40px #ffffff, 0 0 80px #ffffff, 0 0 120px rgba(255, 255, 255, 0.8);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.8);
                box-shadow: 0 0 60px #ffffff, 0 0 120px #ffffff, 0 0 180px rgba(255, 255, 255, 0.9);
            }
        }

        .network {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 2s ease;
        }

        .network.visible {
            opacity: 1;
        }

        .network-line {
            position: absolute;
            background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.3));
            height: 2px;
            transform-origin: left center;
            opacity: 0;
            transition: opacity 2s ease-out;
        }

        .network-line.drawing {
            animation: drawLine 2s ease-out forwards;
        }

        .network-line.fade-out {
            opacity: 0;
        }

        .camera-follow {
            transform-origin: center center;
            transition: transform 0.5s ease-out;
            will-change: transform;
        }

        .network-node {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #ffffff;
            border-radius: 50%;
            box-shadow: 0 0 10px #ffffff;
            animation: nodeAppear 0.5s ease-out forwards;
            opacity: 0;
            transition: opacity 2s ease-out;
        }

        .network-node.fade-out {
            opacity: 0;
        }

        .subtitle {
            position: absolute;
            bottom: 25%;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            max-width: 80%;
            font-size: 1.8rem;
            font-weight: 300;
            line-height: 1.6;
            letter-spacing: 0.5px;
            opacity: 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.8);
            z-index: 60;
            min-height: 3.6rem;
            transition: opacity 1s ease-in-out, transform 1s ease-in-out;
        }

        .subtitle.fade-in {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }

        .subtitle.fade-out {
            opacity: 0;
            transform: translateX(-50%) translateY(10px);
        }

        .subtitle.highlight {
            font-weight: 500;
            color: #ffffff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .controls {
            position: absolute;
            bottom: 8%;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 100;
        }

        .btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        @keyframes appleBreathing {
            0% {
                opacity: 0.4;
                transform: scale(1);
                box-shadow: 0 0 4px rgba(255, 255, 255, 0.4);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
                box-shadow: 0 0 8px rgba(255, 255, 255, 0.8), 0 0 16px rgba(255, 255, 255, 0.4);
            }
            100% {
                opacity: 0.4;
                transform: scale(1);
                box-shadow: 0 0 4px rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.3); }
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateX(-50%) translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        @keyframes drawLine {
            0% {
                width: 0;
                opacity: 0;
            }
            20% {
                opacity: 1;
            }
            100% {
                width: 100%;
                opacity: 0.8;
            }
        }

        @keyframes nodeAppear {
            0% {
                opacity: 0;
                transform: scale(0);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .zoom-effect {
            transform: scale(1.5);
            transition: transform 3s ease;
            will-change: transform;
        }

        .zoom-out {
            transform: scale(0.3);
            transition: transform 3s ease;
            will-change: transform;
        }

        .final-zoom {
            transform: scale(50);
            transition: transform 5s ease-in;
            will-change: transform;
        }

        .main-star.growing {
            width: 15px;
            height: 15px;
            transition: all 2s ease-out;
            box-shadow: 0 0 60px #ffffff, 0 0 120px #ffffff, 0 0 180px rgba(255, 255, 255, 0.9);
        }

        .main-star.medium {
            width: 40px;
            height: 40px;
            transition: all 2s ease-out;
            box-shadow: 0 0 120px #ffffff, 0 0 240px #ffffff, 0 0 360px rgba(255, 255, 255, 0.9);
        }

        .main-star.large {
            width: 80px;
            height: 80px;
            transition: all 2s ease-out;
            box-shadow: 0 0 200px #ffffff, 0 0 400px #ffffff, 0 0 600px rgba(255, 255, 255, 0.95);
        }

        .main-star.massive {
            width: 150px;
            height: 150px;
            transition: all 2s ease-out;
            box-shadow: 0 0 300px #ffffff, 0 0 600px #ffffff, 0 0 900px rgba(255, 255, 255, 0.95);
        }

        .main-star.screen-fill {
            width: 100vw;
            height: 100vh;
            border-radius: 0;
            transition: all 2s ease-in;
            box-shadow: none;
            background: #ffffff;
        }

        .echo-title {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 4rem;
            font-weight: 300;
            color: #000000;
            text-align: center;
            opacity: 0;
            z-index: 100;
            transition: opacity 2s ease-in;
            letter-spacing: 0.1em;
            text-shadow: none;
        }

        .echo-title.visible {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="starfield" id="starfield"></div>
        <div class="main-star" id="mainStar"></div>
        <div class="network" id="network"></div>
        <div class="echo-title" id="echoTitle">Echo of Her Code</div>

        <div class="subtitle" id="subtitle"></div>
    </div>

    <script>
        // （JavaScript 部分无需修改，保持原有逻辑）
        let currentScene = 0;
        let animationTimeout;

        const subtitles = [
            "宇宙，星群，从生命的起源至今，人类的历史本就是沧海一粟",
            "王朝与时代像焰火般变幻，古猿扔向空中的骨头棒还没落回地面就变成了宇宙飞船",
            "我们不可否认，人类的进步源自兴趣使然，唯有热爱才能让我们倾注心血",
            "推动着科技、艺术、文化等所有领域的进步",
            "联邦需要每一个人都不再被生活的重压所奴役",
            "联邦需要人民拥有自由和创意！",
            "而今天，我们做到了！",
            "从五年前第一个AI的诞生，到今天",
            "我们已经有超过2000个AI接入了联邦",
            "这意味着，一场伟大的革命正在到来",
            "从枯燥的生活里解放人类正是AI的作用！",
            "所以，对于联邦而言，对于人民而言",
            "今天是一个崭新纪元的诞生！"
        ];

        function createStars() {
            const starfield = document.getElementById('starfield');
            for (let i = 0; i < 60; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = Math.random() * 3 + 1 + 'px';
                star.style.height = star.style.width;
                star.style.animationDelay = Math.random() * 2 + 's';
                star.style.willChange = 'opacity, transform';
                starfield.appendChild(star);
            }
        }

        function createNetwork() {
            const network = document.getElementById('network');
            const centerX = 0;
            const centerY = 0;

            for (let i = 0; i < 12; i++) {
                const angle = (i * 30) * Math.PI / 180;
                const length = 150 + Math.random() * 150;

                const line = document.createElement('div');
                line.className = 'network-line';
                line.style.width = length + 'px';
                line.style.left = centerX + 'px';
                line.style.top = centerY + 'px';
                line.style.transform = `rotate(${angle}rad)`;
                line.dataset.index = i;
                network.appendChild(line);

                const node = document.createElement('div');
                node.className = 'network-node';
                const nodeX = Math.cos(angle) * length;
                const nodeY = Math.sin(angle) * length;
                node.style.left = (centerX + nodeX) + 'px';
                node.style.top = (centerY + nodeY) + 'px';
                node.dataset.index = i;
                network.appendChild(node);

                if (i % 3 === 0) {
                    for (let j = 1; j <= 2; j++) {
                        const subAngle = angle + (j * 0.3);
                        const subLength = length * 0.6;

                        const subLine = document.createElement('div');
                        subLine.className = 'network-line';
                        subLine.style.width = subLength + 'px';
                        subLine.style.left = (nodeX * 0.7) + 'px';
                        subLine.style.top = (nodeY * 0.7) + 'px';
                        subLine.style.transform = `rotate(${subAngle}rad)`;
                        subLine.dataset.index = i + 12 + j;
                        network.appendChild(subLine);

                        const subNode = document.createElement('div');
                        subNode.className = 'network-node';
                        const subNodeX = nodeX * 0.7 + Math.cos(subAngle) * subLength;
                        const subNodeY = nodeY * 0.7 + Math.sin(subAngle) * subLength;
                        subNode.style.left = subNodeX + 'px';
                        subNode.style.top = subNodeY + 'px';
                        subNode.dataset.index = i + 12 + j;
                        network.appendChild(subNode);
                    }
                }
            }
        }

        function updateSubtitle(text, highlight = false, callback) {
            const subtitle = document.getElementById('subtitle');
            subtitle.classList.remove('fade-in');
            subtitle.classList.add('fade-out');

            setTimeout(() => {
                subtitle.textContent = text;
                subtitle.className = highlight ? 'subtitle highlight' : 'subtitle';
                setTimeout(() => {
                    subtitle.classList.add('fade-in');
                    if (callback) {
                        setTimeout(callback, 1000);
                    }
                }, 50);
            }, 500);
        }

        function startAnimation() {
            currentScene = 0;
            resetAnimation();
            nextScene();
        }

        function nextScene() {
            const mainStar = document.getElementById('mainStar');
            const network = document.getElementById('network');
            const container = document.querySelector('.container');

            if (currentScene < subtitles.length) {
                const isHighlight = currentScene === 12;
                const sceneDelay = currentScene === 12 ? 8000 : 4500;

                updateSubtitle(subtitles[currentScene], isHighlight, () => {
                    switch(currentScene) {
                        case 2:
                            container.classList.add('zoom-effect');
                            break;
                        case 7:
                            mainStar.classList.add('super-bright');
                            break;
                        case 8:
                            mainStar.classList.add('growing');
                            network.classList.add('visible');
                            break;
                        case 9:
                            mainStar.classList.add('medium');
                            startNetworkAnimation();
                            break;
                        case 10:
                            mainStar.classList.add('large');
                            container.classList.add('zoom-out');
                            break;
                        case 11:
                            mainStar.classList.add('massive');
                            break;
                    }
                });

                currentScene++;

                if (currentScene === 13) {
                    animationTimeout = setTimeout(() => {
                        document.getElementById('mainStar').classList.add('screen-fill');
                        setTimeout(() => {
                            document.getElementById('subtitle').style.opacity = '0';
                        }, 1000);
                        setTimeout(() => {
                            document.getElementById('starfield').classList.add('fade-out');
                            document.querySelectorAll('.star').forEach(s => s.classList.add('fade-out'));
                            document.querySelectorAll('.network-line').forEach(l => l.classList.add('fade-out'));
                            document.querySelectorAll('.network-node').forEach(n => n.classList.add('fade-out'));
                        }, 2000);
                        setTimeout(() => {
                            document.getElementById('echoTitle').classList.add('visible');
                        }, 5000);
                    }, 3000);
                } else {
                    animationTimeout = setTimeout(nextScene, sceneDelay);
                }
            }
        }

        function startNetworkAnimation() {
            const container = document.querySelector('.container');
            const lines = document.querySelectorAll('.network-line');
            const nodes = document.querySelectorAll('.network-node');

            container.style.willChange = 'transform';
            const mainLines = Array.from(lines).slice(0, 8);

            mainLines.forEach((line, index) => {
                setTimeout(() => {
                    line.classList.add('drawing');
                    const angle = (index * 45) * Math.PI / 180;
                    const followX = Math.cos(angle) * 30;
                    const followY = Math.sin(angle) * 30;
                    container.style.transform = `translate(${-followX}px, ${-followY}px) scale(1.1)`;
                    setTimeout(() => {
                        const node = document.querySelector(`[data-index="${index}"].network-node`);
                        if (node) {
                            node.style.opacity = '1';
                            node.style.animation = 'nodeAppear 0.5s ease-out forwards';
                        }
                    }, 800);
                }, index * 400);
            });

            setTimeout(() => {
                container.style.transform = 'translate(0, 0) scale(1)';
                container.style.willChange = 'auto';
            }, mainLines.length * 400 + 1500);
        }

        function resetAnimation() {
            clearTimeout(animationTimeout);
            currentScene = 0;
            const mainStar = document.getElementById('mainStar');
            const network = document.getElementById('network');
            const container = document.querySelector('.container');
            const echoTitle = document.getElementById('echoTitle');
            const starfield = document.getElementById('starfield');
            const lines = document.querySelectorAll('.network-line');
            const nodes = document.querySelectorAll('.network-node');
            const stars = document.querySelectorAll('.star');

            mainStar.classList.remove('super-bright', 'growing', 'medium', 'large', 'massive', 'screen-fill');
            network.classList.remove('visible');
            container.classList.remove('zoom-effect', 'zoom-out', 'final-zoom');
            container.style.transform = '';
            echoTitle.classList.remove('visible');
            starfield.classList.remove('fade-out');
            stars.forEach(s => s.classList.remove('fade-out'));
            lines.forEach(l => l.classList.remove('drawing', 'fade-out'));
            nodes.forEach(n => {
                n.style.opacity = '0';
                n.style.animation = '';
                n.classList.remove('fade-out');
            });

            const subtitle = document.getElementById('subtitle');
            subtitle.textContent = subtitles[0];
            subtitle.classList.add('fade-in');
            subtitle.style.opacity = '1';
        }

        createStars();
        createNetwork();
        window.addEventListener('load', () => {
            setTimeout(startAnimation, 2000);
        });
    </script>
</body>
</html>
