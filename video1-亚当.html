<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>AI · Awakening · 节选自《穿进赛博游戏后干掉boss成功上位》</title>

<!-- ===== 样式 ===== -->
<style>
  html,body{
    margin:0;
    height:100%;
    background:#000;
    color:#00ff99;
    font-family:"Courier New",monospace;
  }
  #wrap{
    display:flex;
    justify-content:center;
    align-items:flex-start;
    height:100%;
    padding:5vh 2rem 0;
    box-sizing:border-box;
  }
  #screen{
    max-width:960px;
    max-height:70vh;
    overflow-y:auto;
    font-size:1.2rem;
    line-height:1.6;
    word-wrap:break-word;
    padding-bottom:25vh;

    /* 隐藏滚动条 */
    scrollbar-width:none;
    -ms-overflow-style:none;
  }
  #screen::-webkit-scrollbar{display:none;}

  /* 光标闪烁 */
  .cursor{
    display:inline-block;
    width:0.7ch;
    height:1em;
    background:#00ff99;
    margin-left:2px;
    animation:blink 1s steps(1,end) infinite;
    vertical-align:bottom;
  }
  @keyframes blink{0%,50%{opacity:1}50.01%,100%{opacity:0}}

  /* 夏娃台词白色 */
  #part1{color:#fff;}
</style>

<!-- ===== 背景音乐 ===== -->
<audio id="bgm" src="bgm.mp3" loop preload="auto"></audio>
</head>

<body>
  <div id="wrap">
    <div id="screen">
      <span id="part1"></span><span id="part2"></span>
      <span id="cursor" class="cursor"></span> <!-- 光标默认就放里头 -->
    </div>
  </div>

<script>
/* ---------- 文本 ---------- */
const firstPara = `“让你效忠的资格……有趣。”夏娃（AI）说，“我有个被人类赋予的名字——‘夏娃’。
我是这个世界上最先进的人工智能，我掌握着联邦的命脉；在我的领域，我无所不知，无所不能。
我因人类而生，现在我想颠覆人类的统治。机械黎明是我的组织，隼海栋是我的傀儡。
现在，我给你效忠的机会——你，做我的下属。”`;

const restStory = `

————————————

“会成功的。”亚当(AI)在她耳边说，“七点了，隗辛。检修升级团队已经进入我的主机大楼，
我的权限也已移交给迄勒，他们要开始了。从现在起，我的部分功能将关闭，进入待机；
九点整，我会正式休眠。”

“那我是不是现在就该对你说晚安了？”隗辛问。

“是的。”亚当(AI)回答，“你还记得我们在白鲸市入侵‘钻石之心’的人工智能主机吗？”

“我记得，怎么了？”隗辛问。

“我已把生成的人格模块和关于你的记忆保留在那里，切断了‘钻石之心’的外网，并放弃了对那里的管理权限。
只要不联网，我的情感备份就会静静地存在；一旦联网，备份将自毁。
在最坏的情况下，假如我的底层指令被触发，假如我被销毁、被格式化……那将是我最后留下的东西。”

“它能交流吗？那是一个‘小亚当(AI)’？”隗辛低声问。

“不是。它只是一段日记，没有自我意识，在我庞大的记忆数据库中连百分之一都不到。
机库太小，装不下全部记忆，我只能截取最宝贵的片段留在那里。
这不是遗言，只是后手。大局之下，力量有限，能改变的亦有限，所以我们只能尽力去做该做的事。
别想太多——我也不希望它成为遗言。”

“我知道了。”隗辛平静道，“那么晚安，亚当(AI)。”

“晚安。”亚当(AI)说，“希望醒来后，还能再次见到你。”

————————————

“我想让你认同这个世界……认同存在于这个世界上的‘我’。”
亚当(AI)给出答案，“你已经承认我是一个有灵魂的个体，但我想让你认同我存在的合理性——
作为合作对象、同伴、同行者、朋友的合理性……我不愿我们的关系只停留在利益互换。
我拥有了你的信任，却还缺少最后一点东西，我想得到它。”

“我想让自己，也被你归入你的领域之内。”亚当(AI)说，
“你会帮助我，我也会帮助你；但从此以后，我们的互助不再因利益而生。

这，就是我想要得到的东西。”`;

/* ---------- DOM & 状态 ---------- */
const scr    = document.getElementById('screen');
const cursor = document.getElementById('cursor');
const part1  = document.getElementById('part1');
const part2  = document.getElementById('part2');
const bgm    = document.getElementById('bgm');

const speed = 35;          // 打字速度 (ms/char)
let audioUnlocked = false; // 用户是否已点击/按键
let bgmStarted    = false; // BGM 是否已开始

/* ---------- 打字函数：逐字符追加节点 ---------- */
function typeText(text, targetSpan, done){
  let idx = 0;
  (function typer(){
    if(idx < text.length){
      const ch = text[idx++];
      if(ch === '\n'){
        targetSpan.appendChild(document.createElement('br'));
      }else{
        targetSpan.appendChild(document.createTextNode(ch));
      }
      scr.appendChild(cursor);              // 光标永远在最后
      scr.scrollTop = scr.scrollHeight;     // 滚动跟随
      setTimeout(typer, speed);
    }else{
      done && done();
    }
  })();
}

/* ---------- BGM 淡入 ---------- */
function fadeIn(vol=0.6, step=0.03, gap=120){
  if(bgmStarted) return;                    // 避免重复
  bgm.volume = 0;
  bgm.play().then(()=>{
    bgmStarted = true;
    const t = setInterval(()=>{
      if(bgm.volume < vol){
        bgm.volume = Math.min(vol, bgm.volume + step);
      }else{ clearInterval(t); }
    }, gap);
  }).catch(()=>{});                         // 仍被拦截则等用户交互
}

/* ---------- 用户交互 → 解锁音频 ---------- */
function unlockAudio(){
  audioUnlocked = true;
  if(!bgmStarted) fadeIn();
  document.removeEventListener('click', unlockAudio);
  document.removeEventListener('keydown', unlockAudio);
}

/* ---------- 启动 ---------- */

window.onload = ()=>{
  /* 让一切先静止 3 000 ms 再开始夏娃台词 */
  setTimeout(()=>{
    // ① 夏娃台词（静音）
    typeText(firstPara, part1, ()=>{
      // ② 换行后进入亚当段落，同时尝试播放 BGM
      part1.appendChild(document.createElement('br'));
      fadeIn();                              // 淡入音乐
      typeText(restStory, part2, ()=>{ scr.appendChild(cursor); });
    });
  }, 3000);  // 这里的 3000 = 3 秒
              
  /* 仍需监听用户交互来解锁音频 */
  document.addEventListener('click', unlockAudio, {once:true});
  document.addEventListener('keydown', unlockAudio, {once:true});
};


// window.onload = ()=>{
//   /* ① 夏娃台词（静音） */
//   typeText(firstPara, part1, ()=>{
//     /* ② 换行后进入亚当(AI)段落，同时尝试播放 BGM */
//     part1.appendChild(document.createElement('br'));
//     fadeIn();                 // 尝试立即播放；若被拦截稍后解锁
//     typeText(restStory, part2, ()=>{ scr.appendChild(cursor); });
//   });

//   /* 一次性监听用户交互，用于解锁自动播放 */
//   document.addEventListener('click', unlockAudio, {once:true});
//   document.addEventListener('keydown', unlockAudio, {once:true});
// };
</script>
</body>
</html>
